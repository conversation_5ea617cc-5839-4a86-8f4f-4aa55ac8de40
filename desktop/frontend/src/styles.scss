@tailwind base;
@tailwind components;
@tailwind utilities;

// Custom CSS variables for consistent theming
:root {
  --background-color: #f9fafb;
  --surface-color: #ffffff;
  --primary-text: #111827;
  --secondary-text: #6b7280;
  --border-color: #e5e7eb;
  --hover-color: #f3f4f6;
  --card-background: #ffffff;
  --toolbar-background: #3b82f6;
  --toolbar-text: #ffffff;
  --button-text: #3b82f6;
  --icon-color: #6b7280;
}

// Dark theme variables
.dark {
  --background-color: #111827;
  --surface-color: #1f2937;
  --primary-text: #f9fafb;
  --secondary-text: #d1d5db;
  --border-color: #374151;
  --hover-color: #374151;
  --card-background: #1f2937;
  --toolbar-background: #1e40af;
  --toolbar-text: #ffffff;
  --button-text: #60a5fa;
  --icon-color: #d1d5db;
}

// Global styles
* {
  box-sizing: border-box;
}
html,
body {
  height: 100%;
  margin: 0;
  font-family: "Roboto", "Helvetica Neue", sans-serif;
  background: var(--background-color);
  color: var(--primary-text);
}

// Custom utility classes
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap;
  }

  .btn-outline {
    @apply border border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap;
  }

  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }

  .select-field {
    @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }

  .tab-button {
    @apply px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 border-b-2 border-transparent hover:border-gray-300 dark:hover:border-gray-600 transition-colors duration-200;
  }

  .tab-button-active {
    @apply px-4 py-2 text-sm font-medium text-primary-600 dark:text-primary-400 border-b-2 border-primary-600 dark:border-primary-400;
  }

  .bottom-nav-item {
    @apply flex flex-col items-center justify-center py-2 px-3 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200;
  }

  .bottom-nav-item-active {
    @apply flex flex-col items-center justify-center py-2 px-3 text-xs font-medium text-primary-600 dark:text-primary-400;
  }
}

// Custom scrollbar styling
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

// Hide scrollbar utility class
.hide-scrollbar {
  /* Hide scrollbar for Chrome, Safari and Opera */
  &::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

// Console output styling
.console-output {
  @apply font-mono text-sm bg-gray-900 text-green-400 p-4 rounded-lg overflow-auto max-h-96;
}

// Animation utilities
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

// Material Snackbar custom styles
.success-snackbar {
  --mdc-snackbar-container-color: #4caf50;
  --mdc-snackbar-supporting-text-color: #ffffff;
  --mat-snack-bar-button-color: #ffffff;
}

.info-snackbar {
  --mdc-snackbar-container-color: #2196f3;
  --mdc-snackbar-supporting-text-color: #ffffff;
  --mat-snack-bar-button-color: #ffffff;
}

.warning-snackbar {
  --mdc-snackbar-container-color: #ff9800;
  --mdc-snackbar-supporting-text-color: #ffffff;
  --mat-snack-bar-button-color: #ffffff;
}

.error-snackbar {
  --mdc-snackbar-container-color: #f44336;
  --mdc-snackbar-supporting-text-color: #ffffff;
  --mat-snack-bar-button-color: #ffffff;
}

.critical-snackbar {
  --mdc-snackbar-container-color: #9c27b0;
  --mdc-snackbar-supporting-text-color: #ffffff;
  --mat-snack-bar-button-color: #ffffff;
  box-shadow: 0 4px 20px rgba(156, 39, 176, 0.4);
}
