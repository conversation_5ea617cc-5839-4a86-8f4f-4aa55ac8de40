import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatChipsModule } from '@angular/material/chips';
import { Subscription } from 'rxjs';
import { ErrorService, ErrorNotification, ErrorSeverity } from '../../services/error.service';
import { LucideAngularModule, AlertCircle, AlertTriangle, Info, X } from 'lucide-angular';

@Component({
  selector: 'app-error-display',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatExpansionModule,
    MatChipsModule,
    LucideAngularModule
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div class="error-display-container" *ngIf="activeErrors.length > 0">
      <mat-card class="error-card" *ngFor="let error of activeErrors; trackBy: trackByErrorId">
        <mat-card-header>
          <div mat-card-avatar [class]="'error-icon ' + error.severity">
            <lucide-icon 
              [img]="getErrorIcon(error.severity)" 
              [size]="20">
            </lucide-icon>
          </div>
          <mat-card-title>{{ error.title }}</mat-card-title>
          <mat-card-subtitle>
            {{ error.timestamp | date:'short' }}
            <mat-chip class="severity-chip" [class]="'severity-' + error.severity">
              {{ error.severity.toUpperCase() }}
            </mat-chip>
          </mat-card-subtitle>
          <button 
            mat-icon-button 
            class="dismiss-button"
            (click)="dismissError(error.id)"
            aria-label="Dismiss error">
            <lucide-icon [img]="XIcon" [size]="16"></lucide-icon>
          </button>
        </mat-card-header>
        
        <mat-card-content>
          <p class="error-message">{{ error.message }}</p>
          
          <mat-expansion-panel *ngIf="error.details" class="details-panel">
            <mat-expansion-panel-header>
              <mat-panel-title>Details</mat-panel-title>
            </mat-expansion-panel-header>
            <pre class="error-details">{{ error.details }}</pre>
          </mat-expansion-panel>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .error-display-container {
      position: fixed;
      top: 80px;
      right: 16px;
      width: 400px;
      max-height: calc(100vh - 100px);
      overflow-y: auto;
      z-index: 1000;
      pointer-events: none;
    }

    .error-card {
      margin-bottom: 8px;
      pointer-events: auto;
      border-left: 4px solid;
      animation: slideIn 0.3s ease-out;
    }

    .error-card.info {
      border-left-color: #2196F3;
    }

    .error-card.warning {
      border-left-color: #FF9800;
    }

    .error-card.error {
      border-left-color: #F44336;
    }

    .error-card.critical {
      border-left-color: #9C27B0;
      box-shadow: 0 4px 20px rgba(156, 39, 176, 0.3);
    }

    .error-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      width: 40px;
      height: 40px;
    }

    .error-icon.info {
      background-color: rgba(33, 150, 243, 0.1);
      color: #2196F3;
    }

    .error-icon.warning {
      background-color: rgba(255, 152, 0, 0.1);
      color: #FF9800;
    }

    .error-icon.error {
      background-color: rgba(244, 67, 54, 0.1);
      color: #F44336;
    }

    .error-icon.critical {
      background-color: rgba(156, 39, 176, 0.1);
      color: #9C27B0;
    }

    .severity-chip {
      margin-left: 8px;
      font-size: 10px;
      height: 20px;
      line-height: 20px;
    }

    .severity-info {
      background-color: #E3F2FD;
      color: #1976D2;
    }

    .severity-warning {
      background-color: #FFF3E0;
      color: #F57C00;
    }

    .severity-error {
      background-color: #FFEBEE;
      color: #D32F2F;
    }

    .severity-critical {
      background-color: #F3E5F5;
      color: #7B1FA2;
    }

    .dismiss-button {
      margin-left: auto;
    }

    .error-message {
      margin: 0 0 16px 0;
      word-wrap: break-word;
    }

    .details-panel {
      margin-top: 8px;
    }

    .error-details {
      background-color: #f5f5f5;
      padding: 12px;
      border-radius: 4px;
      font-size: 12px;
      white-space: pre-wrap;
      word-wrap: break-word;
      max-height: 200px;
      overflow-y: auto;
    }

    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    @media (max-width: 768px) {
      .error-display-container {
        width: calc(100vw - 32px);
        right: 16px;
        left: 16px;
      }
    }
  `]
})
export class ErrorDisplayComponent implements OnInit, OnDestroy {
  activeErrors: ErrorNotification[] = [];
  private subscription?: Subscription;

  // Lucide icons
  AlertCircleIcon = AlertCircle;
  AlertTriangleIcon = AlertTriangle;
  InfoIcon = Info;
  XIcon = X;

  constructor(
    private errorService: ErrorService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.subscription = this.errorService.errors$.subscribe(errors => {
      this.activeErrors = errors.filter(error => !error.dismissed);
      this.cdr.detectChanges();
    });
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  dismissError(errorId: string): void {
    this.errorService.dismissError(errorId);
  }

  getErrorIcon(severity: ErrorSeverity): any {
    switch (severity) {
      case ErrorSeverity.INFO:
        return this.InfoIcon;
      case ErrorSeverity.WARNING:
        return this.AlertTriangleIcon;
      case ErrorSeverity.ERROR:
      case ErrorSeverity.CRITICAL:
        return this.AlertCircleIcon;
      default:
        return this.AlertCircleIcon;
    }
  }

  trackByErrorId(index: number, error: ErrorNotification): string {
    return error.id;
  }
}
