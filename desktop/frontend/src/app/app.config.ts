import { ApplicationConfig, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import { provideHttpClient, HTTP_INTERCEPTORS } from "@angular/common/http";
import { provideAnimations } from "@angular/platform-browser/animations";
import { GlobalErrorHandler } from "./services/global-error-handler.service";
import { ErrorInterceptor } from "./interceptors/error.interceptor";

export const appConfig: ApplicationConfig = {
  providers: [
    provideAnimations(),
    provideHttpClient(),
    {
      provide: HTTP_INTERCEPTORS,
      useClass: ErrorInterceptor,
      multi: true,
    },
    {
      provide: ErrorHandler,
      useClass: GlobalErrorHandler,
    },
  ],
};
