<!-- Profiles List -->
<div class="p-6 max-w-4xl mx-auto">
  <!-- Header with import/export buttons - always visible -->
  <div class="mb-6">
    <div class="flex items-center justify-between mb-2">
      <div class="flex items-center space-x-3">
        <lucide-icon
          [img]="UsersIcon"
          class="w-6 h-6 text-primary-600"
        ></lucide-icon>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
          Sync Profiles
        </h1>
      </div>
      <div class="flex items-center space-x-2">
        <button
          (click)="importProfiles()"
          class="btn-secondary flex items-center space-x-2"
          title="Import Profiles"
          >
          <lucide-icon [img]="UploadIcon" class="w-4 h-4"></lucide-icon>
          <span>Import</span>
        </button>
        <button
          (click)="exportProfiles()"
          class="btn-secondary flex items-center space-x-2"
          title="Export Profiles"
          >
          <lucide-icon [img]="DownloadIcon" class="w-4 h-4"></lucide-icon>
          <span>Export</span>
        </button>
      </div>
    </div>
    <p class="text-gray-600 dark:text-gray-400">
      {{ (appService.configInfo$ | async)?.profiles?.length || 0 }} profile(s)
      configured
    </p>
  </div>

  <!-- Profiles content -->
  @if ((appService.configInfo$ | async)?.profiles?.length) {
    <div
      class="card"
      >
      <div class="space-y-3">
        @for (
          profile of (appService.configInfo$ | async)?.profiles; track
          profile; let idx = $index) {
          <div
            (click)="editProfile(idx)"
            class="flex items-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors duration-200 group"
            >
            <div class="flex-shrink-0 mr-4">
              <lucide-icon
                [img]="FolderOpenIcon"
                class="w-8 h-8 text-gray-600 dark:text-gray-400"
              ></lucide-icon>
            </div>
            <div class="flex-1 min-w-0">
              <h3
                class="text-lg font-medium text-gray-900 dark:text-gray-100 truncate"
                >
                {{ profile.name || "Untitled Profile" }}
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {{ getProfileDescription(profile) }}
              </p>
            </div>
            <div class="flex-shrink-0 ml-4">
              <button
                (click)="removeProfile(idx); $event.stopPropagation()"
                class="p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-100 dark:hover:bg-red-900 rounded-lg transition-colors duration-200 opacity-0 group-hover:opacity-100"
                title="Delete profile"
                >
                <lucide-icon [img]="Trash2Icon" class="w-5 h-5"></lucide-icon>
              </button>
            </div>
          </div>
        }
      </div>
    </div>
  } @else {
    <div class="p-6 max-w-4xl mx-auto">
      <div class="card text-center">
        <div class="mb-6">
          <div class="flex justify-center mb-4">
            <div
              class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center"
              >
              <div class="relative">
                <lucide-icon
                  [img]="FolderOpenIcon"
                  class="w-8 h-8 text-gray-400"
                ></lucide-icon>
                <lucide-icon
                  [img]="XIcon"
                  class="w-4 h-4 text-gray-400 absolute -top-1 -right-1"
                ></lucide-icon>
              </div>
            </div>
          </div>
          <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            No Profiles Found
          </h2>
          <p class="text-gray-600 dark:text-gray-400 mb-6">
            Create your first sync profile to get started
          </p>
        </div>
        <div>
          <p class="text-gray-700 dark:text-gray-300 mb-6">
            Profiles allow you to configure different sync settings for various
            directories and remotes.
          </p>
          <button class="btn-primary" (click)="addProfile()">
            <div class="flex items-center space-x-2">
              <lucide-icon [img]="PlusIcon" class="w-5 h-5 mr-2"></lucide-icon>
              Create First Profile
            </div>
          </button>
        </div>
      </div>
    </div>
  }
</div>

<!-- No Profiles State -->

<!-- Floating Action Button -->
<button
  (click)="addProfile()"
  class="fixed bottom-24 right-6 w-14 h-14 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center z-50"
  title="Add Profile"
  >
  <lucide-icon [img]="PlusIcon" class="w-6 h-6"></lucide-icon>
</button>
